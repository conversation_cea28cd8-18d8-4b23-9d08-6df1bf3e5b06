/** @type {import('next').NextConfig} */
const nextConfig = {
  // output: "export", // Ensures static export
  // basePath: "/kapoorsoftwaresolutions", // Use your repository name
  images: {
    // unoptimized: true, // Required for GitHub Pages
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'spaces.kapoorsoftware.com',
        port: '',
        pathname: '/kapoorsoftware/media/**',
      },
    ],
  },
  async redirects() {
    return [
      {
        source: '/mobile-app-development',
        destination: '/Services/mobile-app-development',
        permanent: true, // 301 redirect for SEO
      },
      // Add more redirects here as you move other pages to Services folder
      // Example:
      // {
      //   source: '/web-app-development',
      //   destination: '/Services/web-app-development',
      //   permanent: true,
      // },
    ];
  },
};

export default nextConfig;
